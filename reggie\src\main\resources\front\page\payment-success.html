<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
    <title>支付成功 - 瑞吉外卖</title>
    <link rel="icon" href="./../images/favico.ico">
    <!--不同屏幕尺寸根字体设置-->
    <script src="./../js/base.js"></script>
    <!--element-ui的样式-->
    <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
    <!--引入vant样式-->
    <link rel="stylesheet" href="../styles/vant.min.css"/>
    <!-- 引入样式  -->
    <link rel="stylesheet" href="../styles/index.css" />
    <!--本页面内容的样式-->
    <link rel="stylesheet" href="./../styles/payment-success.css" />
</head>
<body>
    <div id="payment-success">
        <div class="success-container">
            <!-- 成功图标 -->
            <div class="success-icon">
                <i class="el-icon-success"></i>
            </div>
            
            <!-- 成功信息 -->
            <div class="success-info">
                <div class="success-title">支付成功</div>
                <div class="success-subtitle">您的订单已提交，我们会尽快为您配送</div>
            </div>
            
            <!-- 订单信息 -->
            <div class="order-info">
                <div class="info-item">
                    <span class="label">订单号：</span>
                    <span class="value">{{orderNumber}}</span>
                </div>
                <div class="info-item">
                    <span class="label">支付金额：</span>
                    <span class="value amount">￥{{paymentAmount}}</span>
                </div>
                <div class="info-item">
                    <span class="label">支付时间：</span>
                    <span class="value">{{paymentTime}}</span>
                </div>
                <div class="info-item">
                    <span class="label">配送地址：</span>
                    <span class="value">{{deliveryAddress}}</span>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <el-button type="default" size="large" @click="goToOrders">查看订单</el-button>
                <el-button type="primary" size="large" @click="goToHome">继续购物</el-button>
            </div>
            
            <!-- 温馨提示 -->
            <div class="tips">
                <div class="tips-title">温馨提示</div>
                <div class="tips-content">
                    <p>• 您的订单正在准备中，预计30-45分钟送达</p>
                    <p>• 如有问题请联系客服：400-123-4567</p>
                    <p>• 感谢您选择瑞吉外卖，祝您用餐愉快！</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue和相关库 -->
    <script src="../../backend/plugins/vue/vue.js"></script>
    <script src="../../backend/plugins/element-ui/index.js"></script>
    <script src="./../js/vant.min.js"></script>
    <script src="../../backend/plugins/axios/axios.min.js"></script>
    <script src="./../js/request.js"></script>
    
    <script>
        new Vue({
            el: "#payment-success",
            data() {
                return {
                    orderNumber: '',
                    paymentAmount: 0,
                    paymentTime: '',
                    deliveryAddress: ''
                }
            },
            created() {
                this.initPaymentInfo();
            },
            methods: {
                // 初始化支付信息
                initPaymentInfo() {
                    // 生成模拟订单号
                    this.orderNumber = 'RG' + Date.now();
                    
                    // 从sessionStorage获取支付信息
                    const lastPayment = sessionStorage.getItem('lastPayment');
                    if (lastPayment) {
                        const paymentInfo = JSON.parse(lastPayment);
                        this.paymentAmount = paymentInfo.amount || 0;
                        this.paymentTime = this.formatDateTime(new Date());
                    } else {
                        // 默认信息
                        this.paymentAmount = 0;
                        this.paymentTime = this.formatDateTime(new Date());
                    }
                    
                    // 获取配送地址
                    const userEmail = sessionStorage.getItem('userEmail');
                    this.deliveryAddress = userEmail ? `配送至：${userEmail}` : '配送地址未设置';
                },
                
                // 格式化日期时间
                formatDateTime(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },
                
                // 查看订单
                goToOrders() {
                    window.location.href = '/front/page/order.html';
                },
                
                // 继续购物
                goToHome() {
                    window.location.href = '/front/index.html';
                }
            }
        });
    </script>
</body>
</html>
