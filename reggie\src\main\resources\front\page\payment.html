<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
    <title>支付 - 瑞吉外卖</title>
    <link rel="icon" href="./../images/favico.ico">
    <!--不同屏幕尺寸根字体设置-->
    <script src="./../js/base.js"></script>
    <!--element-ui的样式-->
    <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
    <!--引入vant样式-->
    <link rel="stylesheet" href="../styles/vant.min.css"/>
    <!-- 引入样式  -->
    <link rel="stylesheet" href="../styles/index.css" />
    <!--本页面内容的样式-->
    <link rel="stylesheet" href="./../styles/payment.css" />
</head>
<body>
    <div id="payment" v-loading="loading">
        <div class="divHead">
            <div class="divTitle">
                <i class="el-icon-arrow-left" @click="goBack"></i>支付订单
            </div>
        </div>
        
        <!-- 订单摘要 -->
        <div class="order-summary">
            <div class="summary-title">订单摘要</div>
            <div class="dish-list">
                <div v-for="item in orderInfo.cartData" :key="item.id" class="dish-item">
                    <div class="dish-name">{{item.name}}</div>
                    <div class="dish-info">
                        <span class="quantity">x{{item.number}}</span>
                        <span class="price">￥{{item.amount}}</span>
                    </div>
                </div>
            </div>
            <div class="address-info">
                <div class="address-title">配送地址</div>
                <div class="address-detail">
                    {{orderInfo.address.consignee}} {{orderInfo.address.phone}}<br>
                    {{orderInfo.address.detail}}
                </div>
            </div>
            <div class="total-amount">
                <span>总计：</span>
                <span class="amount">￥{{orderInfo.totalAmount}}</span>
            </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-methods">
            <div class="method-title">选择支付方式</div>
            <div class="method-options">
                <div :class="['method-option', {active: paymentMethod === 'qrcode'}]" @click="selectPaymentMethod('qrcode')">
                    <div class="method-icon">
                        <i class="el-icon-mobile-phone"></i>
                    </div>
                    <div class="method-info">
                        <div class="method-name">扫码支付</div>
                        <div class="method-desc">使用手机扫描二维码支付</div>
                    </div>
                    <div class="method-radio">
                        <i :class="paymentMethod === 'qrcode' ? 'el-icon-success' : 'el-icon-circle-check'"></i>
                    </div>
                </div>
                
                <div :class="['method-option', {active: paymentMethod === 'password'}]" @click="selectPaymentMethod('password')">
                    <div class="method-icon">
                        <i class="el-icon-lock"></i>
                    </div>
                    <div class="method-info">
                        <div class="method-name">密码支付</div>
                        <div class="method-desc">输入支付密码完成支付</div>
                    </div>
                    <div class="method-radio">
                        <i :class="paymentMethod === 'password' ? 'el-icon-success' : 'el-icon-circle-check'"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 二维码支付区域 -->
        <div v-if="paymentMethod === 'qrcode'" class="qrcode-payment">
            <div class="qrcode-container">
                <div class="qrcode-title">请使用手机扫描二维码支付</div>
                <div class="qrcode-image">
                    <img :src="qrCodeUrl" alt="支付二维码" v-if="qrCodeUrl">
                    <div v-else class="qrcode-loading">
                        <i class="el-icon-loading"></i>
                        <span>生成二维码中...</span>
                    </div>
                </div>
                <div class="qrcode-amount">支付金额：￥{{orderInfo.totalAmount}}</div>
                <div class="qrcode-tips">
                    <p>• 请在5分钟内完成支付</p>
                    <p>• 支付完成后页面将自动跳转</p>
                </div>
            </div>
        </div>

        <!-- 密码支付区域 -->
        <div v-if="paymentMethod === 'password'" class="password-payment">
            <div class="password-container">
                <div class="password-title">请输入支付密码</div>
                <div class="password-input">
                    <el-input 
                        v-model="paymentPassword" 
                        type="password" 
                        placeholder="请输入6位支付密码"
                        maxlength="6"
                        show-password>
                    </el-input>
                </div>
                <div class="password-tips">
                    <p>测试支付密码：123456</p>
                </div>
                <div class="password-actions">
                    <el-button type="primary" size="large" @click="confirmPasswordPayment" :disabled="!paymentPassword">
                        确认支付 ￥{{orderInfo.totalAmount}}
                    </el-button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue和相关库 -->
    <script src="../../backend/plugins/vue/vue.js"></script>
    <script src="../../backend/plugins/element-ui/index.js"></script>
    <script src="./../js/vant.min.js"></script>
    <script src="../../backend/plugins/axios/axios.min.js"></script>
    <script src="./../js/request.js"></script>
    <script src="./../api/payment.js"></script>

    <script>
        new Vue({
            el: "#payment",
            data() {
                return {
                    loading: false,
                    orderInfo: {
                        orderId: null,
                        cartData: [],
                        address: {},
                        totalAmount: 0,
                        remark: ''
                    },
                    paymentMethod: 'password', // 默认选择密码支付
                    paymentPassword: '',
                    qrCodeUrl: '',
                    paymentTimer: null
                }
            },
            created() {
                this.initOrderInfo();
                this.generateQRCode();
            },
            beforeDestroy() {
                if (this.paymentTimer) {
                    clearInterval(this.paymentTimer);
                }
            },
            methods: {
                // 初始化订单信息
                initOrderInfo() {
                    const orderData = sessionStorage.getItem('pendingOrder');
                    if (orderData) {
                        this.orderInfo = JSON.parse(orderData);
                        console.log('订单信息：', this.orderInfo);
                    } else {
                        this.$message.error('订单信息不存在，请重新下单');
                        this.goBack();
                    }
                },

                // 返回上一页
                goBack() {
                    history.go(-1);
                },

                // 选择支付方式
                selectPaymentMethod(method) {
                    this.paymentMethod = method;
                    if (method === 'qrcode' && !this.qrCodeUrl) {
                        this.generateQRCode();
                    }
                },

                // 生成二维码
                async generateQRCode() {
                    if (!this.orderInfo.orderId) return;

                    try {
                        const res = await generateQRCodeApi(this.orderInfo.orderId);
                        if (res.code === 1) {
                            this.qrCodeUrl = res.data;
                            // 开始轮询支付状态
                            this.startPaymentPolling();
                        } else {
                            this.$message.error('生成二维码失败');
                        }
                    } catch (error) {
                        console.error('生成二维码失败：', error);
                        // 使用默认二维码图片
                        this.qrCodeUrl = '/front/images/default-qrcode.png';
                    }
                },

                // 开始轮询支付状态
                startPaymentPolling() {
                    if (this.paymentTimer) {
                        clearInterval(this.paymentTimer);
                    }

                    this.paymentTimer = setInterval(async () => {
                        try {
                            const res = await getPaymentStatusApi(this.orderInfo.orderId);
                            if (res.code === 1 && res.data.status === 'success') {
                                clearInterval(this.paymentTimer);
                                this.goToPaymentSuccess();
                            }
                        } catch (error) {
                            console.error('查询支付状态失败：', error);
                        }
                    }, 2000); // 每2秒查询一次
                },

                // 确认密码支付
                async confirmPasswordPayment() {
                    if (!this.paymentPassword) {
                        this.$message.warning('请输入支付密码');
                        return;
                    }

                    this.loading = true;
                    try {
                        const paymentData = {
                            orderId: this.orderInfo.orderId,
                            password: this.paymentPassword
                        };

                        const res = await passwordPaymentApi(paymentData);
                        if (res.code === 1) {
                            this.$message.success('支付成功');
                            setTimeout(() => {
                                this.goToPaymentSuccess();
                            }, 1000);
                        } else {
                            this.$message.error(res.msg || '支付失败');
                        }
                    } catch (error) {
                        console.error('支付失败：', error);
                        this.$message.error('支付失败，请重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 跳转到支付成功页面
                goToPaymentSuccess() {
                    // 清除订单信息
                    sessionStorage.removeItem('pendingOrder');
                    // 跳转到支付成功页面
                    window.location.href = '/front/page/payment-success.html';
                }
            }
        });
    </script>
</body>
</html>
