# 500异常问题排查指南

## 问题描述
用户输入QQ邮箱和验证码后，系统显示500异常。

## 可能的原因和解决方案

### 1. 数据库表结构问题
**问题**：user表可能没有email字段
**解决方案**：
```sql
-- 执行以下SQL语句
USE db_reggie;

-- 检查email字段是否存在
SHOW COLUMNS FROM user LIKE 'email';

-- 如果不存在，添加email字段
ALTER TABLE `user` ADD COLUMN `email` varchar(100) DEFAULT NULL COMMENT '邮箱地址' AFTER `phone`;

-- 插入测试用户
INSERT INTO `user` (`id`, `email`, `status`) VALUES (1548719795, '<EMAIL>', 1) 
ON DUPLICATE KEY UPDATE `email` = '<EMAIL>';
```

### 2. 邮件服务配置问题
**问题**：QQ邮箱SMTP配置可能有误
**检查**：
- 确认QQ邮箱授权码是否正确：sqbuacoozgshhhfa
- 确认SMTP服务器配置：smtp.qq.com:587
- 检查网络是否能访问QQ邮箱服务器

### 3. 应用启动问题
**检查步骤**：
1. 确认Spring Boot应用是否正常启动
2. 访问测试接口：http://localhost:8080/test/hello
3. 检查控制台日志是否有错误信息

### 4. 前端参数传递问题
**检查**：
- 打开浏览器开发者工具
- 查看Network标签页
- 确认发送的请求参数格式是否正确

## 调试步骤

### 步骤1：检查应用状态
访问：http://localhost:8080/test/hello
如果返回"系统运行正常"，说明应用启动正常。

### 步骤2：检查数据库
```sql
USE db_reggie;
DESCRIBE user;
SELECT * FROM user WHERE email = '<EMAIL>';
```

### 步骤3：测试验证码发送
1. 在登录页面输入：<EMAIL>
2. 点击"获取验证码"
3. 查看控制台日志，确认验证码是否生成和发送

### 步骤4：测试登录
1. 输入邮箱：<EMAIL>
2. 输入收到的验证码
3. 点击登录
4. 查看控制台日志和浏览器Network面板

## 日志查看
关键日志信息：
- "发送验证码请求 - 邮箱：xxx"
- "邮箱验证码：xxx，发送至：xxx"
- "验证码已存储到Session"
- "登录请求参数：xxx"
- "登录验证 - 邮箱：xxx，验证码：xxx"
- "Session中的验证码：xxx"

## 常见错误和解决方案

### 错误1：NullPointerException
**原因**：前端发送的参数为null
**解决**：检查前端form数据结构

### 错误2：Column 'email' doesn't exist
**原因**：数据库表没有email字段
**解决**：执行数据库更新脚本

### 错误3：Mail server connection failed
**原因**：邮件服务器连接失败
**解决**：检查网络连接和SMTP配置

### 错误4：验证码不匹配
**原因**：Session中的验证码与输入的不一致
**解决**：检查验证码生成和存储逻辑

## 紧急修复方案
如果邮件发送有问题，可以临时使用固定验证码：

在UserController的sendMsg方法中临时修改：
```java
// 临时使用固定验证码用于测试
String code = "123456";
log.info("使用固定验证码进行测试：{}", code);
```

这样可以先测试登录流程是否正常。
