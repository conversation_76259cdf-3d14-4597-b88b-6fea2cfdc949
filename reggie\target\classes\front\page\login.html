<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
        <title>菩提阁</title>
        <link rel="icon" href="./../images/favico.ico">
        <!--不同屏幕尺寸根字体设置-->
        <script src="./../js/base.js"></script>
        <!--element-ui的样式-->
        <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
        <!--引入vant样式-->
        <link rel="stylesheet" href="../styles/vant.min.css"/>
        <!-- 引入样式  -->
        <link rel="stylesheet" href="../styles/index.css" />
        <!--本页面内容的样式-->
        <link rel="stylesheet" href="./../styles/login.css" />
      </head>
    <body>
        <div id="login" v-loading="loading">
            <div class="divHead">登录</div>
            <div class="divContainer">
                <el-input placeholder=" 请输入邮箱地址" v-model="form.email"  maxlength='50'/></el-input>
                <div class="divSplit"></div>
                <el-input placeholder=" 请输入验证码" v-model="form.code"  maxlength='20'/></el-input>
                <span @click='getCode'>获取验证码</span>
            </div>
            <div class="divMsg" v-if="msgFlag">邮箱地址输入不正确，请重新输入</div>
            <el-button type="primary" :class="{btnSubmit:1===1,btnNoEmail:!form.email,btnEmail:form.email}" @click="btnLogin">登录</el-button>
        </div>
        <!-- 开发环境版本，包含了有帮助的命令行警告 -->
        <script src="../../backend/plugins/vue/vue.js"></script>
        <!-- 引入组件库 -->
        <script src="../../backend/plugins/element-ui/index.js"></script>
        <!-- 引入vant样式 -->
        <script src="./../js/vant.min.js"></script>  
        <!-- 引入axios -->
        <script src="../../backend/plugins/axios/axios.min.js"></script>
        <script src="./../js/request.js"></script>
        <script src="./../api/login.js"></script>
    </body>
    <script>
        new Vue({
            el:"#login",
            data(){
                return {
                    form:{
                        email:'',
                        code:''
                    },
                    msgFlag:false,
                    loading:false
                }
            },
            computed:{},
            created(){},
            mounted(){},
            methods:{
                sendMsg(data){
                    return $axios({
                        'url': '/user/sendMsg',
                        'method': 'post',
                        data
                    })
                },
                getCode(){
                    this.form.code = ''
                    //这里使用正则表达式对邮箱进行格式校验
                    const emailRegex = /^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$/;
                    //this.form:  {email:<EMAIL>}
                    if (emailRegex.test(this.form.email)) {
                        //校验通过
                        this.msgFlag = false
                        //自定义发邮件的请求，需要传递邮箱到后台
                        this.sendMsg(this.form);//{email:<EMAIL>}
                    }else{
                        this.msgFlag = true
                    }
                },
                async btnLogin(){
                    if(this.form.email && this.form.code){
                        this.loading = true
                        //this.form 本身就是json格式的数据：{email:<EMAIL>,code:123456}
                        const res = await loginApi(this.form)
                        this.loading = false
                        if(res.code === 1){
                            sessionStorage.setItem("userEmail",this.form.email)
                            window.requestAnimationFrame(()=>{
                                window.location.href= '/front/index.html'
                            })
                        }else{
                            this.$notify({ type:'warning', message:res.msg});
                        }
                    }else{
                        this.$notify({ type:'warning', message:'请输入邮箱地址'});
                    }
                }
            }
        })
    </script>
</html>