# 瑞吉外卖系统传统E-R图文本描述

## E-R图符号说明
- 【矩形】：表示实体
- 【菱形】：表示关系
- 【椭圆】：表示属性
- 【下划线】：表示主键属性
- 【虚线椭圆】：表示派生属性
- 【双线椭圆】：表示多值属性

## 实体及其属性

### 【USER】实体
```
┌─────────────────────────────────────┐
│                USER                 │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ email (UK) ←── 邮箱验证登录核心    │
│ ○ phone                            │
│ ○ name                             │
│ ○ sex                              │
│ ○ id_number                        │
│ ○ avatar                           │
│ ○ status                           │
│ ○ create_time                      │
│ ○ update_time                      │
│ ○ create_user                      │
│ ○ update_user                      │
└─────────────────────────────────────┘
```

### 【ADDRESS_BOOK】实体
```
┌─────────────────────────────────────┐
│            ADDRESS_BOOK             │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ user_id (FK)                     │
│ ○ consignee                        │
│ ○ phone                            │
│ ○ sex                              │
│ ○ province_code                    │
│ ○ province_name                    │
│ ○ city_code                        │
│ ○ city_name                        │
│ ○ district_code                    │
│ ○ district_name                    │
│ ○ detail                           │
│ ○ label                            │
│ ○ is_default                       │
│ ○ create_time                      │
│ ○ update_time                      │
│ ○ create_user                      │
│ ○ update_user                      │
└─────────────────────────────────────┘
```

### 【ORDERS】实体
```
┌─────────────────────────────────────┐
│               ORDERS                │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ number (UK)                      │
│ ○ status ←── 订单状态管理           │
│ ○ user_id (FK)                     │
│ ○ address_book_id (FK)             │
│ ○ order_time                       │
│ ○ checkout_time                    │
│ ○ pay_time ←── 支付功能新增字段     │
│ ○ pay_method                       │
│ ○ amount                           │
│ ○ remark                           │
│ ○ user_name                        │
│ ○ phone                            │
│ ○ address                          │
│ ○ consignee                        │
└─────────────────────────────────────┘
```

### 【ORDER_DETAIL】实体
```
┌─────────────────────────────────────┐
│            ORDER_DETAIL             │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ name                             │
│ ○ order_id (FK)                    │
│ ○ dish_id                          │
│ ○ setmeal_id                       │
│ ○ dish_flavor                      │
│ ○ number                           │
│ ○ amount                           │
│ ○ image                            │
└─────────────────────────────────────┘
```

### 【PAYMENT】实体
```
┌─────────────────────────────────────┐
│              PAYMENT                │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ order_id (FK)                    │
│ ○ transaction_id (UK)              │
│ ○ payment_method ←── 支付方式       │
│ ○ amount                           │
│ ○ status ←── 支付状态跟踪           │
│ ○ payment_time                     │
│ ○ qr_code_url ←── 二维码支付       │
│ ○ remark                           │
│ ○ create_time                      │
│ ○ update_time                      │
└─────────────────────────────────────┘
```

### 【DISH】实体
```
┌─────────────────────────────────────┐
│                DISH                 │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ name                             │
│ ○ category_id                      │
│ ○ price                            │
│ ○ code                             │
│ ○ image                            │
│ ○ description                      │
│ ○ status                           │
│ ○ sort                             │
│ ○ create_time                      │
│ ○ update_time                      │
│ ○ create_user                      │
│ ○ update_user                      │
└─────────────────────────────────────┘
```

### 【SETMEAL】实体
```
┌─────────────────────────────────────┐
│              SETMEAL                │
├─────────────────────────────────────┤
│ ○ id (PK)                          │
│ ○ category_id                      │
│ ○ name                             │
│ ○ price                            │
│ ○ status                           │
│ ○ code                             │
│ ○ description                      │
│ ○ image                            │
│ ○ create_time                      │
│ ○ update_time                      │
│ ○ create_user                      │
│ ○ update_user                      │
└─────────────────────────────────────┘
```

## 关系及其基数

### 1. USER ◇─────◇ ADDRESS_BOOK
```
【USER】 ──── ◇ 拥有 ◇ ──── 【ADDRESS_BOOK】
   1                           N
   
关系属性：无
基数：一对多 (1:N)
说明：一个用户可以拥有多个收货地址
```

### 2. USER ◇─────◇ ORDERS
```
【USER】 ──── ◇ 下单 ◇ ──── 【ORDERS】
   1                         N
   
关系属性：下单时间
基数：一对多 (1:N)
说明：一个用户可以下多个订单
```

### 3. ADDRESS_BOOK ◇─────◇ ORDERS
```
【ADDRESS_BOOK】 ──── ◇ 配送到 ◇ ──── 【ORDERS】
       1                                N
       
关系属性：无
基数：一对多 (1:N)
说明：一个地址可以被多个订单使用
```

### 4. ORDERS ◇─────◇ ORDER_DETAIL
```
【ORDERS】 ──── ◇ 包含 ◇ ──── 【ORDER_DETAIL】
     1                           N
     
关系属性：无
基数：一对多 (1:N)
说明：一个订单包含多个订单明细
```

### 5. ORDERS ◇─────◇ PAYMENT
```
【ORDERS】 ──── ◇ 支付 ◇ ──── 【PAYMENT】
     1                          1
     
关系属性：支付时间、支付方式
基数：一对一 (1:1)
说明：一个订单对应一条支付记录
```

### 6. DISH ◇─────◇ ORDER_DETAIL
```
【DISH】 ──── ◇ 订购 ◇ ──── 【ORDER_DETAIL】
   1                          N
   
关系属性：购买数量、购买价格
基数：一对多 (1:N)
说明：一个菜品可以被多个订单明细引用
```

### 7. SETMEAL ◇─────◇ ORDER_DETAIL
```
【SETMEAL】 ──── ◇ 订购 ◇ ──── 【ORDER_DETAIL】
     1                            N
     
关系属性：购买数量、购买价格
基数：一对多 (1:N)
说明：一个套餐可以被多个订单明细引用
```

## 完整E-R图结构图
```
                    ┌─────────┐
                    │  USER   │
                    │  (用户)  │
                    └────┬────┘
                         │ 1
                    ◇ 拥有 ◇
                         │ N
                    ┌────┴────┐
                    │ADDRESS_ │
                    │  BOOK   │
                    │ (地址簿) │
                    └────┬────┘
                         │ 1
                    ◇ 配送到 ◇
                         │ N
    ┌─────────┐     ┌────┴────┐     ┌─────────┐
    │  DISH   │ 1   │ ORDERS  │ 1   │PAYMENT  │
    │ (菜品)  │──◇──│ (订单)  │──◇──│ (支付)  │
    └─────────┘ 订购 └────┬────┘ 支付 └─────────┘
                         │ 1
                    ◇ 包含 ◇
                         │ N
                    ┌────┴────┐
                    │ORDER_   │
                    │DETAIL   │
                    │(订单详情)│
                    └────┬────┘
                         │ N
                    ◇ 订购 ◇
                         │ 1
                    ┌────┴────┐
                    │SETMEAL  │
                    │ (套餐)  │
                    └─────────┘
```

## 关键设计特点

### 1. 邮箱验证登录体现
- USER实体的email属性作为唯一键
- 无需password字段
- 通过邮箱验证码实现安全登录

### 2. 数据库持久化支付体现
- PAYMENT实体独立存在
- ORDERS实体增加pay_time字段
- 支持完整的支付状态跟踪

### 3. 数据完整性保证
- 主键约束确保实体唯一性
- 外键约束确保引用完整性
- 冗余字段确保历史数据完整性

### 4. 业务逻辑支持
- 订单状态流转管理
- 多种支付方式支持
- 地址信息管理
- 商品信息快照保存

这个E-R图设计完整体现了瑞吉外卖系统的核心业务逻辑和数据关系，支持邮箱验证登录和数据库持久化支付功能。
