<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
        <title>菩提阁</title>
        <link rel="icon" href="./../images/favico.ico">
        <!--不同屏幕尺寸根字体设置-->
        <script src="./../js/base.js"></script>
        <!--element-ui的样式-->
        <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
        <!--引入vant样式-->
        <link rel="stylesheet" href="../styles/vant.min.css"/>
        <!-- 引入样式  -->
        <link rel="stylesheet" href="../styles/index.css" />
        <!--本页面内容的样式-->
        <link rel="stylesheet" href="./../styles/address-edit.css" />
    </head>
    <body>
        <div id="address_edit" class="app">
            <div class="divHead">
                <div class="divTitle">
                    <i class="el-icon-arrow-left" @click="goBack"></i>{{title}}
                </div>
            </div>
            <div class="divContent">
                <div class="divItem">
                   <span>联系人：</span> 
                   <el-input placeholder=" 请填写收货人的姓名" v-model="form.consignee"  maxlength='10' class="inputUser"/></el-input>
                   <span class="spanChecked" @click="form.sex = '1'">
                    <i :class="{iActive:form.sex === '1'}"></i>
                    先生
                   </span>
                   <span class="spanChecked" @click="form.sex = '0'">
                    <i :class="{iActive:form.sex === '0'}"></i>
                    女士
                </span>
                </div>
                <div class="divItem">
                    <span>手机号：</span> 
                    <el-input placeholder=" 请填写收货人手机号码" v-model="form.phone"  maxlength='20' style="width: calc(100% - 80rem);"/></el-input>
                </div>
                <div class="divItem">
                    <span>收货地址：</span> 
                    <el-input placeholder=" 请输入收货地址" v-model="form.detail"  maxlength='140'/></el-input>
                </div>
                <div class="divItem ">
                    <span>标签：</span> 
                    <span v-for="(item,index) in labelList" :key="index" @click="form.label = item;activeIndex = index" :class="{spanItem:true,spanActiveSchool:activeIndex === index}">{{item}}</span>
                </div>
                <div class="divSave" @click="saveAddress">保存地址</div>
                <div class="divDelete" @click="deleteAddress" v-if="id">删除地址</div>
            </div>
        </div>
        <!-- 开发环境版本,包含了有帮助的命令行警告 -->
        <script src="../../backend/plugins/vue/vue.js"></script>
        <!-- 引入组件库 -->
        <script src="../../backend/plugins/element-ui/index.js"></script>
        <!-- 引入vant样式 -->
        <script src="./../js/vant.min.js"></script>       
        <script src="./../js/common.js"></script>
        <script src="./../api/address.js"></script>
        <!-- 引入axios -->
        <script src="../../backend/plugins/axios/axios.min.js"></script>
        <script src="./../js/request.js"></script>
        <script>
            new Vue({
                el:"#address_edit",
                data(){
                    return {
                        title:'新增收货地址',
                        form:{
                            consignee:'',//联系人
                            phone:undefined,//手机号
                            sex:'1',//0表示女 1 表示男
                            detail:'',//收货地址
                            label:'公司',//标签
                        },
                        labelList:[
                            '无','公司','家','学校'
                        ],
                        id:undefined,
                        activeIndex :0
                    }
                },
                computed:{},
                created(){
                    this.initData()
                },
                mounted(){
                },
                methods:{
                    goBack(){
                        history.go(-1)
                    },
                    async initData(){
                        const params = parseUrl(window.location.search)
                        this.id = params.id
                        if(params.id){
                            this.title = '编辑收货地址'
                            const res = await addressFindOneApi(params.id)
                            if(res.code === 1){
                                this.form = res.data
                            }else{
                                this.$notify({ type:'warning', message:res.msg});
                            }
                        }
                    },
                    async saveAddress(){
                        const form = this.form
                        if(!form.consignee){
                            this.$notify({ type:'warning', message:'请输入联系人'});
                            return 
                        }
                        if(!form.phone){
                            this.$notify({ type:'warning', message:'请输入手机号'});
                            return 
                        }
                        if(!form.detail){
                            this.$notify({ type:'warning', message:'请输入收货地址'});
                            return 
                        }
                        const reg = /^1[3|4|5|7|8][0-9]{9}$/
                        if(!reg.test(form.phone)){
                            this.$notify({ type:'warning', message:'手机号码不合法'});
                            return  
                        }
                        let res= {}
                        if(this.id){
                            res = await updateAddressApi(this.form)
                        }else{
                            res = await addAddressApi(this.form)
                        }
                        
                        if(res.code === 1){
                            window.requestAnimationFrame(()=>{
                                window.location.replace('/front/page/address.html')
                            })
                        }else{
                            this.$notify({ type:'warning', message:res.msg});
                        }
                    },
                    deleteAddress(){
                        this.$dialog.confirm({
                            title: '确认删除',
                            message: '确认要删除当前地址吗？',
                        })
                        .then( async () => {
                            const res = await deleteAddressApi({ids:this.id })
                            if(res.code === 1){
                                window.requestAnimationFrame(()=>{
                                    window.location.replace('/front/page/address.html')
                                })
                            }else{
                                this.$notify({ type:'warning', message:res.msg});
                            }
                        })
                        .catch(() => {
                        });
                    },
                }
            })
            </script>
    </body>
</html>
