package com.reggie.service;

import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * 支付服务接口
 */
public interface PaymentService {
    
    /**
     * 生成支付二维码
     * @param orderId 订单ID
     * @return 二维码图片URL
     */
    String generateQRCode(Long orderId);
    
    /**
     * 处理密码支付
     * @param orderId 订单ID
     * @param session Session
     * @return 支付结果
     */
    boolean processPasswordPayment(Long orderId, HttpSession session);
    
    /**
     * 查询支付状态
     * @param orderId 订单ID
     * @return 支付状态信息
     */
    Map<String, Object> getPaymentStatus(Long orderId);
}
