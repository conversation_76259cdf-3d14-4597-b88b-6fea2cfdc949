# 瑞吉外卖系统 E-R 图详细说明

## 概述
本文档详细描述了瑞吉外卖系统的实体关系图（E-R图），展示了系统中核心实体及其相互关系，体现了邮箱验证登录和数据库持久化支付功能的数据模型设计。

## 核心实体详解

### 1. USER（用户实体）
**描述**：系统用户信息，支持邮箱验证登录
**主键**：id (bigint)
**唯一键**：email (varchar) - 支持邮箱登录

**核心属性**：
- `id`：用户唯一标识（主键）
- `email`：邮箱地址（唯一键，用于登录验证）
- `phone`：手机号码
- `name`：用户姓名
- `sex`：性别
- `status`：用户状态（0-禁用，1-启用）
- `create_time`：创建时间
- `update_time`：更新时间

**业务特点**：
- 支持邮箱验证码登录
- 无需密码存储，通过邮箱验证
- 用户状态管理

### 2. ADDRESS_BOOK（地址簿实体）
**描述**：用户收货地址信息
**主键**：id (bigint)
**外键**：user_id → USER.id

**核心属性**：
- `id`：地址唯一标识（主键）
- `user_id`：关联用户ID（外键）
- `consignee`：收货人姓名
- `phone`：收货人电话
- `province_name/city_name/district_name`：省市区信息
- `detail`：详细地址
- `is_default`：是否默认地址（0-否，1-是）

**业务特点**：
- 支持多地址管理
- 默认地址设置
- 完整的地址层级结构

### 3. ORDERS（订单实体）
**描述**：订单主表，记录订单基本信息
**主键**：id (bigint)
**唯一键**：number (varchar) - 订单号
**外键**：user_id → USER.id, address_book_id → ADDRESS_BOOK.id

**核心属性**：
- `id`：订单唯一标识（主键）
- `number`：订单号（唯一键）
- `status`：订单状态（1-待付款，2-待派送，3-已派送，4-已完成，5-已取消）
- `user_id`：下单用户ID（外键）
- `address_book_id`：配送地址ID（外键）
- `order_time`：下单时间
- `pay_time`：支付时间（新增字段，支持支付功能）
- `pay_method`：支付方式（1-微信，2-支付宝，3-密码支付）
- `amount`：实收金额
- `consignee/phone/address`：冗余的收货信息

**业务特点**：
- 完整的订单生命周期管理
- 支持多种支付方式
- 地址信息冗余存储确保数据完整性

### 4. ORDER_DETAIL（订单详情实体）
**描述**：订单明细表，记录订单中的具体商品信息
**主键**：id (bigint)
**外键**：order_id → ORDERS.id

**核心属性**：
- `id`：明细唯一标识（主键）
- `order_id`：关联订单ID（外键）
- `dish_id`：菜品ID
- `setmeal_id`：套餐ID
- `name`：商品名称
- `dish_flavor`：菜品口味
- `number`：购买数量
- `amount`：商品金额
- `image`：商品图片

**业务特点**：
- 支持菜品和套餐两种商品类型
- 记录购买时的商品快照信息
- 支持口味选择

### 5. PAYMENT（支付实体）
**描述**：支付记录表，记录支付相关信息
**主键**：id (bigint)
**唯一键**：transaction_id (varchar) - 交易号
**外键**：order_id → ORDERS.id

**核心属性**：
- `id`：支付记录唯一标识（主键）
- `order_id`：关联订单ID（外键）
- `transaction_id`：交易号（唯一键）
- `payment_method`：支付方式（password-密码支付，qrcode-二维码支付）
- `amount`：支付金额
- `status`：支付状态（pending-进行中，success-成功，failed-失败）
- `payment_time`：支付时间
- `qr_code_url`：二维码URL（用于二维码支付）

**业务特点**：
- 支持多种支付方式
- 完整的支付状态跟踪
- 支持二维码支付展示

### 6. DISH（菜品实体）
**描述**：菜品信息表
**主键**：id (bigint)

**核心属性**：
- `id`：菜品唯一标识（主键）
- `name`：菜品名称
- `category_id`：菜品分类ID
- `price`：菜品价格
- `image`：菜品图片
- `description`：菜品描述
- `status`：菜品状态（0-停售，1-起售）

### 7. SETMEAL（套餐实体）
**描述**：套餐信息表
**主键**：id (bigint)

**核心属性**：
- `id`：套餐唯一标识（主键）
- `name`：套餐名称
- `category_id`：套餐分类ID
- `price`：套餐价格
- `image`：套餐图片
- `description`：套餐描述
- `status`：套餐状态（0-停售，1-起售）

## 实体关系详解

### 1. USER ↔ ADDRESS_BOOK（一对多）
**关系类型**：1:N
**关系描述**：一个用户可以拥有多个收货地址
**外键**：ADDRESS_BOOK.user_id → USER.id
**业务规则**：
- 用户可以添加多个收货地址
- 可以设置默认收货地址
- 删除用户时需要处理关联地址

### 2. USER ↔ ORDERS（一对多）
**关系类型**：1:N
**关系描述**：一个用户可以下多个订单
**外键**：ORDERS.user_id → USER.id
**业务规则**：
- 用户可以下多个订单
- 订单记录用户信息快照
- 支持订单历史查询

### 3. ADDRESS_BOOK ↔ ORDERS（一对多）
**关系类型**：1:N
**关系描述**：一个地址可以被多个订单使用
**外键**：ORDERS.address_book_id → ADDRESS_BOOK.id
**业务规则**：
- 订单使用用户的收货地址
- 地址信息在订单中冗余存储
- 地址修改不影响历史订单

### 4. ORDERS ↔ ORDER_DETAIL（一对多）
**关系类型**：1:N
**关系描述**：一个订单包含多个订单明细
**外键**：ORDER_DETAIL.order_id → ORDERS.id
**业务规则**：
- 订单可以包含多种商品
- 明细记录商品购买时的信息快照
- 订单金额等于所有明细金额之和

### 5. ORDERS ↔ PAYMENT（一对一）
**关系类型**：1:1
**关系描述**：一个订单对应一条支付记录
**外键**：PAYMENT.order_id → ORDERS.id
**业务规则**：
- 每个订单只能有一次支付
- 支付成功后更新订单状态
- 支持支付失败重试

### 6. DISH ↔ ORDER_DETAIL（一对多）
**关系类型**：1:N
**关系描述**：一个菜品可以被多个订单明细引用
**外键**：ORDER_DETAIL.dish_id → DISH.id
**业务规则**：
- 菜品信息在订单明细中快照存储
- 菜品价格变更不影响历史订单
- 支持菜品口味选择

### 7. SETMEAL ↔ ORDER_DETAIL（一对多）
**关系类型**：1:N
**关系描述**：一个套餐可以被多个订单明细引用
**外键**：ORDER_DETAIL.setmeal_id → SETMEAL.id
**业务规则**：
- 套餐信息在订单明细中快照存储
- 套餐价格变更不影响历史订单
- 订单明细中dish_id和setmeal_id互斥

## 数据完整性约束

### 主键约束
- 每个实体都有唯一的主键标识
- 使用bigint类型支持大数据量

### 外键约束
- 保证数据引用完整性
- 级联删除策略需要谨慎设计

### 唯一性约束
- USER.email：确保邮箱唯一性
- ORDERS.number：确保订单号唯一性
- PAYMENT.transaction_id：确保交易号唯一性

### 业务约束
- 订单状态流转规则
- 支付金额与订单金额一致性
- 地址信息完整性验证

## 索引设计建议

### 主要索引
```sql
-- 用户邮箱索引（登录查询）
CREATE INDEX idx_user_email ON user(email);

-- 订单用户索引（用户订单查询）
CREATE INDEX idx_orders_user_id ON orders(user_id);

-- 订单状态索引（状态筛选）
CREATE INDEX idx_orders_status ON orders(status);

-- 订单时间索引（时间范围查询）
CREATE INDEX idx_orders_time ON orders(order_time);

-- 支付订单索引（支付查询）
CREATE INDEX idx_payment_order_id ON payment(order_id);

-- 订单明细索引（订单详情查询）
CREATE INDEX idx_order_detail_order_id ON order_detail(order_id);
```

## 系统特色体现

### 邮箱验证登录
- USER实体的email字段作为唯一登录标识
- 无需密码字段，通过邮箱验证码实现安全登录
- 支持用户状态管理

### 数据库持久化支付
- PAYMENT实体完整记录支付信息
- ORDERS实体的pay_time字段记录支付时间
- 支持多种支付方式（密码支付、二维码支付）
- 完整的支付状态跟踪

### 业务数据完整性
- 地址信息在订单中冗余存储
- 商品信息在订单明细中快照保存
- 支持完整的订单生命周期管理

这个E-R图设计充分体现了瑞吉外卖系统的业务特点，支持邮箱验证登录和数据库持久化支付功能，确保了数据的完整性和一致性。
