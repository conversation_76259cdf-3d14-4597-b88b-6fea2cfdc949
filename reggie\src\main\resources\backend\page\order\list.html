<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/element-ui/index.css"/>
    <link rel="stylesheet" href="../../styles/common.css"/>
    <link rel="stylesheet" href="../../styles/page.css"/>
    <style>
        .search-btn {
            margin-left: 20px;
        }

        .tableBar {
            justify-content: flex-start !important;
        }

        .info-box {
            margin: -15px -44px 20px;
        }

        .info-box .item-box {
            display: flex;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            text-align: left;
            margin-bottom: 14px;
        }

        .info-box .item-box:last-child {
            margin-bottom: 0;
        }

        .info-box .item-box .label {
            width: 96px;
        }

        .info-box .item-box .des {
            flex: 1;
            color: #333333;
        }
    </style>
</head>
<body>
<div class="dashboard-container" id="order-app" v-loading="loading">
    <div class="container">
        <!-- 搜索项 -->
        <div class="tableBar">
            <el-input v-model="input" placeholder="请输入订单号" style="width: 250px">
                <i slot="prefix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="init"></i>
            </el-input>
            <el-date-picker v-model="orderTime"
                            clearable
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetimerange"
                            placeholder="选择日期"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            style="width: 400px;margin-left: 20px;"
            ></el-date-picker>
            <el-button type="primary" class="search-btn" @click="init">查询</el-button>
        </div>
        <el-table :data="tableData" stripe class="tableBox">
            <el-table-column prop="number" label="订单号" min-width="110"></el-table-column>
            <el-table-column prop="订单状态" label="订单状态">
                <template slot-scope="{ row }">
                    <span>{{ getOrderType(row) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="consignee" label="收货人"></el-table-column>
            <el-table-column prop="phone" label="手机号"></el-table-column>
            <el-table-column prop="address" label="地址" show-overflow-tooltip></el-table-column>
            <el-table-column prop="orderTime" label="下单时间" min-width="100"></el-table-column>
            <el-table-column prop="amount" label="实收金额">
                <template slot-scope="{ row }">
                    <span>￥{{ row.amount }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="btn" label="操作">
                <template slot-scope="{ row }">
                    <el-button type="text" @click="goDetail(row)" class="blueBug">
                        查看
                    </el-button>
                    <el-divider v-if="row.status === 2" direction="vertical"></el-divider>
                    <el-button v-if="row.status === 2" type="text" @click="cancelOrDeliveryOrComplete(3, row.id)"
                               class="blueBug">
                        派送
                    </el-button>
                    <el-divider v-if="row.status === 3" direction="vertical"></el-divider>
                    <el-button v-if="row.status === 3" type="text" @click="cancelOrDeliveryOrComplete(4, row.id)"
                               class="blueBug">
                        完成
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                class="pageList"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pageSize"
                :current-page.sync="page"
                layout="total, sizes, prev, pager, next, jumper"
                :total="counts"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
        ></el-pagination>
    </div>

    <!-- 查看弹框部分 -->
    <el-dialog
            title="订单信息"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
    >
        <div class="info-box">
            <div class="item-box">
                <span class="label">订单号：</span>
                <span class="des">{{ diaForm.number }}</span>
            </div>
            <div class="item-box">
                <span class="label">订单状态：</span>
                <span class="des">{{ getOrderType(diaForm) }}</span>
            </div>
            <div class="tableBox">
                <span class="label">订单明细：</span>
                <el-table :data="diaForm.orderDetails" stripe class="tableBox">
                    <el-table-column prop="name" label="菜品" min-width="110"></el-table-column>
                    <el-table-column prop="dishFlavor" label="口味"></el-table-column>
                    <el-table-column prop="number" label="数量"></el-table-column>
                    <el-table-column prop="amount" label="金额"></el-table-column>
                </el-table>

                <!-- <div class="item-box"  v-for="(orderDetail,index) in diaForm.orderDetails" :key="index" >
                   <span class="des"> {{orderDetail.name}}</span>
                   <span class="des"> {{orderDetail.dishFlavor}}</span>
                   <span class="des"> x  {{orderDetail.number}}</span>
                   <span class="des"> {{orderDetail.amount}}</span>
                 </div>-->
                <!--          <div  v-for="(orderDetail,index) in diaForm.orderDetails" :key="index" class="item">
                            {{orderDetail.name}}
                            {{orderDetail.dishFlavor}}
                            x {{orderDetail.number}}
                            {{orderDetail.amount}}
                          </div>-->
            </div>
            <div class="item-box">
                <span class="label">支付金额：</span>
                <span class="des">{{ diaForm.amount }}</span>
            </div>
            <div class="item-box">
                <span class="label">收货人：</span>
                <span class="des">{{ diaForm.consignee }}</span>
            </div>
            <div class="item-box">
                <span class="label">联系电话：</span>
                <span class="des">{{ diaForm.phone }}</span>
            </div>
            <div class="item-box">
                <span class="label">地址：</span>
                <span class="des">{{ diaForm.address }}</span>
            </div>

            <div class="item-box">
                <span class="label">下单时间：</span>
                <span class="des">{{ diaForm.orderTime }}</span>
            </div>
        </div>
        <!-- <el-form ref="diaForm" :model="diaForm" class="dia-form">
          <el-form-item label="订单号">
            <span>{{ diaForm.number }}</span>
          </el-form-item>
          <el-form-item label="订单状态">
            <span>{{ getOrderType(diaForm) }}</span>
          </el-form-item>
          <el-form-item label="收货人">
            <span>{{ diaForm.consignee }}</span>
          </el-form-item>
          <el-form-item label="联系电话">
            <span>{{ diaForm.phone }}</span>
          </el-form-item>
          <el-form-item label="地址">
            <span>{{ diaForm.address }}</span>
          </el-form-item>
          <el-form-item label="支付金额">
            <span>{{ diaForm.amount }}</span>
          </el-form-item>
          <el-form-item label="下单时间">
            <span>{{ diaForm.orderTime }}</span>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span> -->
    </el-dialog>
</div>
<!-- 开发环境版本，包含了有帮助的命令行警告 -->
<script src="../../plugins/vue/vue.js"></script>
<!-- 引入组件库 -->
<script src="../../plugins/element-ui/index.js"></script>
<!-- 引入axios -->
<script src="../../plugins/axios/axios.min.js"></script>
<script src="../../js/request.js"></script>
<script src="../../api/order.js"></script>
<script>
    new Vue({
        el: '#order-app',
        data() {
            return {
                input: '',
                orderTime: '',
                beginTime: '',
                endTime: '',
                counts: 0,
                page: 1,
                pageSize: 10,
                tableData: [],
                dialogVisible: false,
                diaForm: {},
                loading: false
            }
        },
        computed: {},
        watch: {
            orderTime(val) {
                if (val && val.length >= 2) {
                    this.beginTime = val[0]
                    this.endTime = val[1]
                } else {
                    this.beginTime = ''
                    this.endTime = ''
                }
            }
        },
        created() {
            this.init()
        },
        mounted() {
            // this.loading = false
        },
        methods: {
            async init() {
                getOrderDetailPage({
                    page: this.page,
                    pageSize: this.pageSize,
                    number: this.input || undefined,
                    beginTime: this.beginTime || undefined,
                    endTime: this.endTime || undefined

                }).then(res => {
                    if (String(res.code) === '1') {
                        this.tableData = res.data.records || []
                        this.counts = res.data.total
                    }
                }).catch(err => {
                    this.$message.error('请求出错了：' + err)
                })
            },
            handleQuery() {
                this.page = 1;
                this.init();
            },
            getOrderType(row) {
                let str = ''
                switch (row.status) {
                    case 1:
                        str = '待付款'
                        break;
                    case 2:
                        str = '待派送'
                        break;
                    case 3:
                        str = '已派送'
                        break;
                    case 4:
                        str = '已完成'
                        break;
                    case 5:
                        str = '已取消'
                        break;

                }
                return str
            },
            // 查看详情
            goDetail(row) {
                this.diaForm = {}
                this.dialogVisible = true
                this.diaForm = {...row}
            },
            // 取消，派送，完成
            cancelOrDeliveryOrComplete(status, id) {
                this.$confirm('确认更改该订单状态?', '提示', {
                    'confirmButtonText': '确定',
                    'cancelButtonText': '取消',
                    'type': 'warning'
                }).then(() => {
                    editOrderDetail(params).then(res => {
                        if (res.code === 1) {
                            this.$message.success(status === 3 ? '订单已派送' : '订单已完成')
                            this.init()
                        } else {
                            this.$message.error(res.msg || '操作失败')
                        }
                    }).catch(err => {
                        this.$message.error('请求出错了：' + err)
                    })
                })
                const params = {
                    status,
                    id
                }
            },
            handleClose() {
                this.dialogVisible = false
            },
            handleSizeChange(val) {
                this.pageSize = val
                this.init()
            },
            handleCurrentChange(val) {
                this.page = val
                this.init()
            }
        }
    })
</script>
</body>
</html>