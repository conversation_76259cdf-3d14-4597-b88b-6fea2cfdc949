<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>瑞吉外卖管理端</title>
    <link rel="shortcut icon" href="favicon.ico">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="plugins/element-ui/index.css" />
    <link rel="stylesheet" href="styles/common.css" />
    <link rel="stylesheet" href="styles/index.css" />
    <link rel="stylesheet" href="styles/icon/iconfont.css" />
    <style>
      .body{
        min-width: 1366px;
      }
      .app-main{
        height: calc(100% - 64px);
      }
      .app-main .divTmp{
        width: 100%;
        height: 100%;
      }
    </style>
  </head>

  <body>
    <div class="app" id="app">
      <div class="app-wrapper openSidebar clearfix">
        <!-- sidebar -->
        <div class="sidebar-container">
          <div class="logo">
            <!-- <img src="images/logo.png" width="122.5" alt="" /> -->
            <img src="images/login/login-logo.png" alt="" style="width: 117px; height: 32px" />
          </div>

          <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu
              :default-active="defAct"
              :unique-opened="false"
              :collapse-transition="false"
              background-color="#343744"
              text-color="#bfcbd9"
              active-text-color="#f4f4f5"
            >
              <div v-for="item in menuList" :key="item.id">
                <el-submenu :index="item.id" v-if="item.children && item.children.length>0">
                  <template slot="title">
                    <i class="iconfont" :class="item.icon"></i>
                    <span>{{item.name}}</span>
                  </template>
                  <el-menu-item
                    v-for="sub in item.children"
                    :index="sub.id"
                    :key="sub.id"
                    @click="menuHandle(sub,false)"
                    >
                    <i :class="iconfont" :class="sub.icon"></i>
                    <span slot="title">{{sub.name}}</span>
                    </el-menu-item
                  >
                </el-submenu>
                <el-menu-item v-else :index="item.id" @click="menuHandle(item,false)">
                  <i class="iconfont" :class="item.icon"></i>
                  <span slot="title">{{item.name}}</span>
                </el-menu-item>
              </div>
            </el-menu>
          </el-scrollbar>
        </div>
        <div class="main-container">
          <!-- <navbar /> -->
          <div class="navbar">
            <div class="head-lable">
              <span v-if="goBackFlag" class="goBack" @click="goBack()"
                ><img src="images/icons/<EMAIL>" alt="" /> 返回</span
              >
              <span>{{headTitle}}</span>
            </div>
            <div class="right-menu">
              <div class="avatar-wrapper">{{ userInfo.name }}</div>
              <!-- <div class="logout" @click="logout">退出</div> -->
              <img src="images/icons/<EMAIL>" class="outLogin" alt="退出" @click="logout" />
            </div>
          </div>
          <div class="app-main" v-loading="loading">
            <div class="divTmp" v-show="loading"></div>
            <iframe
              id="cIframe"
              class="c_iframe"
              name="cIframe"
              :src="iframeUrl"
              width="100%"
              height="auto"
              frameborder="0"
              v-show="!loading"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
    <!-- 开发环境版本，包含了有帮助的命令行警告 -->
    <script src="plugins/vue/vue.js"></script>
    <!-- 引入组件库 -->
    <script src="plugins/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="plugins/axios/axios.min.js"></script>
    <script src="js/request.js"></script>
    <script src="./api/login.js"></script>
    <script>

      new Vue({
        el: '#app',
        data() {
          return {
            defAct: '1',
            menuActived: '1',
            userInfo: {},
            menuList: [
                  {
                    id: '1',
                    name: '分类管理',
                    url: 'page/category/list.html',
                    icon: 'icon-category'
                  },
                  {
                    id: '2',
                    name: '菜品管理',
                    url: 'page/food/list.html',
                    icon: 'icon-food'
                  },
                  {
                    id: '3',
                    name: '套餐管理',
                    url: 'page/combo/list.html',
                    icon: 'icon-combo'
                  },
                  {
                    id: '4',
                    name: '订单明细',
                    url: 'page/order/list.html',
                    icon: 'icon-order'
                  }
              //   ],
              // },
            ],
            iframeUrl: 'page/category/list.html',
            headTitle: '分类管理',
            goBackFlag: false,
            loading: true,
            timer: null
          }
        },
        computed: {},
        created() {
          const userInfo = window.localStorage.getItem('userInfo')
          if (userInfo) {
            this.userInfo = JSON.parse(userInfo)
          }
          this.closeLoading()
        },
        beforeDestroy() {
          this.timer = null
          clearTimeout(this.timer)
        },
        mounted() {
          window.menuHandle = this.menuHandle
        },
        methods: {
          logout() {
            logoutApi().then((res)=>{
              if(res.code === 1){
                localStorage.removeItem('userInfo')
                window.location.href = '/backend/page/login/login.html'
              }
            })
          },
          goBack() {
            // window.location.href = 'javascript:history.go(-1)'
            const menu = this.menuList.find(item=>item.id===this.menuActived)
            // this.goBackFlag = false
            // this.headTitle = menu.name
            this.menuHandle(menu,false)
          },
          menuHandle(item, goBackFlag) {
            this.loading = true
            this.menuActived = item.id
            this.iframeUrl = item.url
            this.headTitle = item.name
            this.goBackFlag = goBackFlag
            this.closeLoading()
          },
          closeLoading(){
            this.timer = null
            this.timer = setTimeout(()=>{
              this.loading = false
            },1000)
          }
        }
      })
    </script>
  </body>
</html>
