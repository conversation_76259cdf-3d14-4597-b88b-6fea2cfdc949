# 邮箱验证功能实现说明

## 功能概述
已成功将原有的手机号短信验证登录系统改为邮箱验证码登录系统。

## 修改内容

### 1. 后端修改

#### 1.1 添加邮件依赖
- 在 `pom.xml` 中添加了 `spring-boot-starter-mail` 依赖

#### 1.2 邮件服务配置
- 在 `application.yaml` 中添加了QQ邮箱SMTP配置：
  - 发送邮箱：<EMAIL>
  - 授权码：sqbuacoozgshhhfa
  - SMTP服务器：smtp.qq.com
  - 端口：587

#### 1.3 User实体类修改
- 在 `User.java` 中添加了 `email` 字段

#### 1.4 创建邮件服务类
- 新建 `EmailService.java`，实现邮件发送功能
- 包含验证码邮件发送方法

#### 1.5 UserController修改
- 修改 `sendMsg` 方法：
  - 改为发送邮箱验证码
  - 添加邮箱格式验证
  - 限制只能使用测试邮箱 <EMAIL>
  - 生成6位验证码
  - 设置验证码有效期5分钟
- 修改 `login` 方法：
  - 改为使用邮箱地址进行登录验证
  - 验证邮箱格式
  - 登录成功后清除验证码

### 2. 前端修改

#### 2.1 登录页面修改 (login.html)
- 将手机号输入框改为邮箱地址输入框
- 更新表单验证逻辑为邮箱格式验证
- 修改数据模型从 `phone` 改为 `email`
- 更新错误提示信息
- 修改sessionStorage存储键名

#### 2.2 CSS样式修改 (login.css)
- 添加了 `btnNoEmail` 和 `btnEmail` 样式类

#### 2.3 API文件修改 (login.js)
- 移除了调试用的 `alert` 代码

#### 2.4 用户页面修改 (user.html)
- 将显示的用户信息从手机号改为邮箱地址

### 3. 数据库修改

#### 3.1 表结构更新
- 创建了 `add_email_column.sql` 脚本
- 为 `user` 表添加 `email` 字段
- 为测试邮箱创建用户记录

## 登录流程

1. 用户在登录页面输入邮箱地址（<EMAIL>）
2. 点击"获取验证码"按钮
3. 系统验证邮箱格式和是否为测试邮箱
4. 生成6位随机验证码并发送到邮箱
5. 验证码存储在session中，有效期5分钟
6. 用户输入收到的验证码
7. 系统验证邮箱和验证码的正确性
8. 验证成功后重定向到订餐页面

## 安全特性

1. **邮箱格式验证**：使用正则表达式验证邮箱格式
2. **测试限制**：当前仅支持测试邮箱 <EMAIL>
3. **验证码有效期**：验证码5分钟后自动失效
4. **一次性使用**：验证码使用后立即清除
5. **异常处理**：邮件发送失败时有相应的错误处理

## 测试说明

### 前置条件
1. 确保数据库已执行 `add_email_column.sql` 脚本
2. 确保邮件服务配置正确
3. 确保QQ邮箱授权码有效

### 测试步骤
1. 启动Spring Boot应用
2. 访问 `http://localhost:8080/front/page/login.html`
3. 输入邮箱地址：<EMAIL>
4. 点击"获取验证码"
5. 检查邮箱收到验证码
6. 输入验证码并点击登录
7. 验证是否成功跳转到订餐页面

## 注意事项

1. 当前仅支持测试邮箱 <EMAIL>
2. QQ邮箱授权码需要定期更新
3. 验证码有效期为5分钟
4. 邮件发送可能有延迟，请耐心等待
5. 确保网络连接正常，能够访问QQ邮箱SMTP服务器
