server:
  port: 8080
spring:
  application:
    #应用的名称，可选
    name: reggie
  # 数据源配置
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: "******************************************************************
      &useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull
      &useSSL=false&allowPublicKeyRetrieval=true"
      username: root
      password: 123456
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: sqbuacoozgshhhfa
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
# MyBatis Plus配置
mybatis-plus:
  configuration:
    #在映射实体或者属性时，将数据库中表名和字段名中的下划线去掉，按照驼峰命名法映射
    map-underscore-to-camel-case: true
    # SQL语句等日志信息输出在控制台
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 使用雪花算法自动生成主键ID
      id-type: ASSIGN_ID
# 常量配置，存放图片的目录
reggie:
  path: D:\springboot\work\reggie\src\main\resources\img\