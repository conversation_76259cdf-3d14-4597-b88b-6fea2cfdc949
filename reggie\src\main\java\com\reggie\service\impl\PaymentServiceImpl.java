package com.reggie.service.impl;

import com.reggie.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支付服务实现类
 */
@Service
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    // 模拟支付状态存储（生产环境应使用数据库或Redis）
    private static final Map<Long, Map<String, Object>> paymentStatusMap = new ConcurrentHashMap<>();

    @Override
    public String generateQRCode(Long orderId) {
        log.info("为订单{}生成支付二维码", orderId);
        
        // 生成随机二维码内容
        String qrContent = generateRandomQRContent(orderId);
        
        // 模拟二维码图片URL（实际项目中应该调用二维码生成服务）
        String qrCodeUrl = "/front/images/qrcode/" + orderId + ".png";
        
        // 存储二维码信息到Session或缓存
        Map<String, Object> qrInfo = new HashMap<>();
        qrInfo.put("orderId", orderId);
        qrInfo.put("qrContent", qrContent);
        qrInfo.put("createTime", LocalDateTime.now());
        qrInfo.put("status", "pending");
        
        paymentStatusMap.put(orderId, qrInfo);
        
        log.info("二维码生成成功，订单ID：{}，二维码内容：{}", orderId, qrContent);
        return qrCodeUrl;
    }

    @Override
    public boolean processPasswordPayment(Long orderId, HttpSession session) {
        log.info("处理密码支付，订单ID：{}", orderId);

        try {
            // 获取当前订单信息
            Map<String, Object> currentOrder = (Map<String, Object>) session.getAttribute("currentOrder");

            // 更新支付状态
            Map<String, Object> paymentInfo = new HashMap<>();
            paymentInfo.put("orderId", orderId);
            paymentInfo.put("paymentMethod", "password");
            paymentInfo.put("paymentTime", LocalDateTime.now());
            paymentInfo.put("status", "success");
            paymentInfo.put("transactionId", generateTransactionId());

            // 如果有订单信息，添加金额
            if (currentOrder != null) {
                paymentInfo.put("amount", 99.00); // 模拟金额，实际应从订单中获取
            }

            paymentStatusMap.put(orderId, paymentInfo);

            // 将支付信息存储到Session
            session.setAttribute("lastPayment", paymentInfo);

            // 更新订单状态为已支付
            if (currentOrder != null) {
                currentOrder.put("status", 2); // 2-待派送
                currentOrder.put("paymentTime", LocalDateTime.now());
                session.setAttribute("currentOrder", currentOrder);
            }

            log.info("密码支付成功，订单ID：{}，交易号：{}", orderId, paymentInfo.get("transactionId"));
            return true;
        } catch (Exception e) {
            log.error("密码支付处理失败：", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getPaymentStatus(Long orderId) {
        log.info("查询订单{}的支付状态", orderId);
        
        Map<String, Object> status = paymentStatusMap.get(orderId);
        if (status == null) {
            status = new HashMap<>();
            status.put("orderId", orderId);
            status.put("status", "not_found");
            status.put("message", "订单不存在");
        }
        
        return status;
    }

    /**
     * 生成随机二维码内容
     */
    private String generateRandomQRContent(Long orderId) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        sb.append("REGGIE_PAY_");
        sb.append(orderId);
        sb.append("_");
        sb.append(System.currentTimeMillis());
        sb.append("_");
        sb.append(random.nextInt(10000));
        return sb.toString();
    }

    /**
     * 生成交易号
     */
    private String generateTransactionId() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Random random = new Random();
        return "TXN" + timestamp + String.format("%04d", random.nextInt(10000));
    }
}
