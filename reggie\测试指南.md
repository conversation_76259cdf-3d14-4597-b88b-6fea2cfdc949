# 简化邮箱验证系统 - 测试指南

## 🎯 测试目标
验证邮箱验证码发送功能和简化登录验证功能是否正常工作。

## ✅ 系统特性
- ✅ 发送真实验证码到邮箱
- ✅ 任何验证码都能通过验证
- ✅ 无需数据库操作
- ✅ 仅支持测试邮箱：<EMAIL>

## 🚀 快速测试步骤

### 步骤1：启动应用
确保Spring Boot应用正常启动，端口8080。

### 步骤2：访问登录页面
打开浏览器访问：`http://localhost:8080/front/page/login.html`

### 步骤3：输入邮箱
在邮箱输入框中输入：`<EMAIL>`

### 步骤4：获取验证码
点击"获取验证码"按钮，系统会：
- 生成6位随机验证码
- 发送邮件到指定邮箱
- 在控制台显示验证码（用于调试）

### 步骤5：输入任意验证码
在验证码输入框中输入任意内容，例如：
- `123456`
- `000000`
- `abcdef`
- 或任何其他内容

### 步骤6：登录
点击"登录"按钮，系统会：
- 自动通过验证（不检查验证码正确性）
- 创建临时用户会话
- 跳转到订餐页面

## 📋 详细测试场景

### 场景1：正常流程测试
1. 邮箱：`<EMAIL>`
2. 点击获取验证码
3. 验证码：`123456`（任意）
4. 点击登录
5. **预期结果**：成功跳转到订餐页面

### 场景2：邮箱格式验证
1. 邮箱：`invalid-email`
2. 点击获取验证码
3. **预期结果**：显示"邮箱格式不正确"

### 场景3：非测试邮箱限制
1. 邮箱：`<EMAIL>`
2. 点击获取验证码
3. **预期结果**：显示"当前仅支持测试邮箱：<EMAIL>"

### 场景4：空验证码测试
1. 邮箱：`<EMAIL>`
2. 验证码：留空
3. 点击登录
4. **预期结果**：显示"请输入邮箱地址"

## 🔍 日志监控

在控制台中查看以下关键日志：

### 发送验证码时：
```
发送验证码请求 - 邮箱：<EMAIL>
生成验证码：123456，发送至邮箱：<EMAIL>
验证码邮件发送成功，收件人：<EMAIL>，验证码：123456
验证码已发送并存储到Session，邮箱：<EMAIL>，验证码：123456
```

### 登录验证时：
```
登录请求参数：{email=<EMAIL>, code=123456}
登录验证 - 邮箱：<EMAIL>，验证码：123456
用户输入验证码：123456，Session中的验证码：654321
验证码验证通过（简化模式），邮箱：<EMAIL>
用户登录成功（无数据库模式），邮箱：<EMAIL>，临时ID：1703123456789
```

## ⚠️ 注意事项

1. **邮件发送延迟**：QQ邮箱可能有1-2分钟延迟
2. **网络连接**：确保能访问smtp.qq.com:587
3. **验证码不重要**：收到的验证码仅供参考，任何输入都能通过
4. **Session管理**：登录状态通过Session维护
5. **无数据库**：系统不会查询或修改任何数据库表

## 🐛 故障排除

### 问题1：邮件发送失败
**症状**：控制台显示"邮件发送失败"
**解决**：
- 检查网络连接
- 确认QQ邮箱授权码是否正确
- 检查防火墙设置

### 问题2：页面无法访问
**症状**：浏览器显示无法连接
**解决**：
- 确认Spring Boot应用已启动
- 检查端口8080是否被占用
- 查看控制台启动日志

### 问题3：验证码输入后无反应
**症状**：点击登录按钮无响应
**解决**：
- 打开浏览器开发者工具查看Network面板
- 检查是否有JavaScript错误
- 确认邮箱格式正确

## 🎉 成功标志

测试成功的标志：
1. ✅ 能够发送验证码到邮箱
2. ✅ 任意验证码都能登录成功
3. ✅ 成功跳转到订餐页面
4. ✅ 控制台显示正确的日志信息
