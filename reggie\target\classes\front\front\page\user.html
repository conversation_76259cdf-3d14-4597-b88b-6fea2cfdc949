<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
    <title>菩提阁</title>
    <link rel="icon" href="./../images/favico.ico">
    <!--element-ui的样式-->
    <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
    <!-- 引入样式  -->
    <link rel="stylesheet" href="../styles/index.css" />
    <!--引入vant样式-->
    <link rel="stylesheet" href="../styles/vant.min.css"/>
    <!--本页面内容的样式-->
    <link rel="stylesheet" href="./../styles/user.css" />
</head>
<body>
    <div id="user" class="app">
        <div class="divHead">
            <div class="divTitle">
                <i class="el-icon-arrow-left" @click="goBack"></i>个人中心
            </div>
            <div class="divUser">
                <img src="./../images/headPage.png"/>
                <div class="desc">
                <!--    <div class="divName">林之迷 <img src="./../images/women.png"/></div>-->
                    <div class="divPhone">{{userPhone}}</div>
                </div>
            </div>
        </div>
        <div class="divContent">
            <div class="divLinks">
                <div class="item" @click="toAddressPage">
                    <img src="./../images/locations.png"/>
                    <span>地址管理</span>
                    <i class="el-icon-arrow-right"></i>
                </div>
                <div class="divSplit"></div>
                <div class="item" @click="toOrderPage">
                    <img src="./../images/orders.png"/>
                    <span>历史订单</span>
                    <i class="el-icon-arrow-right"></i>
                </div>
            </div>
            <div class="divOrders" v-if="order[0]">
                <div class="title">最新订单</div>
                <div class="timeStatus">
                    <span>{{order[0].orderTime}}</span>
                    <span>{{getStatus(order[0].status)}}</span>
                    <!-- <span>正在派送</span> -->
                </div>
                <div class="dishList">
                    <div v-for="(item,index) in order[0].orderDetails" :key="index" class="item">
                        <span>{{item.name}}</span>
                        <span>x{{item.number}}</span>
                    </div>
                </div>
                <div class="result">
                    <span>共{{order[0].sumNum}} 件商品,实付</span>
                    <span class="price">￥{{order[0].amount}}</span>
                </div>
                <div class="btn" v-if="order[0].status === 4">
                    <div class="btnAgain" @click="addOrderAgain">再来一单</div>
                </div>
            </div>
            <div class="quitLogin" @click="toPageLogin">
                退出登录
            </div>
        </div>
    </div>
    <!-- 开发环境版本，包含了有帮助的命令行警告 -->
    <script src="../../backend/plugins/vue/vue.js"></script>
    <!-- 引入组件库 -->
    <script src="../../backend/plugins/element-ui/index.js"></script>
    <!-- 引入vant组件 -->
    <script src="./../js/vant.min.js"></script>    
    <!--不同屏幕尺寸根字体设置-->
    <script src="./../js/base.js"></script>
    <!-- 引入axios -->
    <script src="../../backend/plugins/axios/axios.min.js"></script>
    <script src="./../js/request.js"></script>
    <script src="./../api/order.js"></script>
    <script src="./../api/login.js"></script>
    <script>
        new Vue({
            el:"#user",
            data(){
                return {
                    form:{
                        phone:'',
                        code:''
                    },
                    msgFlag:false,
                    order:[{
                        orderTime:'',//下单时间
                        status:undefined,//订单状态 1已结账，2未结账，3已退单，4已完成，5已取消
                        orderDetails:[{
                            name:'',//菜品名称
                            number:undefined,//数量
                        }],//明细
                        amount:undefined,//实收金额
                        sumNum:0,//菜品总数
                    }],
                }
            },
            computed:{},
            created(){
                this.userPhone =sessionStorage.getItem("userPhone")
                this.initData()
            },
            mounted(){},
            methods:{
                goBack(){
                    history.go(-1)
                },
                toAddressPage(){
                    window.requestAnimationFrame(()=>{
                        window.location.href = '/front/page/address.html'
                    })  
                },
                toOrderPage(){
                    window.requestAnimationFrame(()=>{
                        window.location.href = '/front/page/order.html'
                    }) 
                },
                initData(){
                    this.getLatestOrder()
                },
                async getLatestOrder(){
                    const params = {
                        page:1,
                        pageSize:1
                    }
                    const res = await orderPagingApi(params)
                    if(res.code === 1){
                        this.order = res.data.records
                        if(this.order && this.order[0].orderDetails){
                            let number = 0
                            this.order[0].orderDetails.forEach(item=>{
                                number += item.number
                            })
                            this.order[0].sumNum = number
                        }
                    }else{
                        this.$notify({ type:'warning', message:res.msg});
                    }
                },
                getStatus(status){
                    let str = ''
                    switch(status){
                        case 1:
                            str =  '待付款'
                        break;
                        case 2:
                            str =  '正在派送'
                        break;
                        case 3:
                            str =  '已派送'
                        break;
                        case 4:
                            str =  '已完成'
                        break;
                        case 5:
                            str =  '已取消'
                        break;

                    }
                    return str
                },
                async addOrderAgain(){
                    const res = await orderAgainApi({id:this.order[0].id})
                    if(res.code === 1){
                        window.requestAnimationFrame(()=>{
                            window.location.href = '/front/index.html'
                        }) 
                    }else{
                        this.$notify({ type:'warning', message:res.msg});
                    }
                },
                async toPageLogin(){
                    const res = await loginoutApi()
                    if(res.code === 1){
                        window.requestAnimationFrame(()=>{
                            window.location.href = '/front/page/login.html'
                        }) 
                    }else{
                        this.$notify({ type:'warning', message:res.msg});
                    }                
                }
            }
        })
    </script>
</body>
</html>