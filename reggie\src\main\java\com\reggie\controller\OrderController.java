package com.reggie.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.reggie.common.R;
import com.reggie.dto.OrdersDto;
import com.reggie.entity.OrderDetail;
import com.reggie.entity.Orders;
import com.reggie.service.OrderDetailService;
import com.reggie.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderDetailService orderDetailService;
    /**
     * 订单信息分页查询
     */
    @GetMapping("/page")
    public R<Page<OrdersDto>> page(int page, int pageSize, String number,
                                   String beginTime,String endTime){
        //构造分页构造器对象
        Page<Orders> pageInfo = new Page<>(page,pageSize);
        //条件构造器
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        //添加过滤条件
        queryWrapper.like(number != null,Orders::getNumber,number);
        queryWrapper.between(beginTime!=null && endTime!=null  ,
                Orders::getOrderTime,beginTime,endTime);
        //添加排序条件
        queryWrapper.orderByDesc(Orders::getOrderTime);
        //执行分页查询
        orderService.page(pageInfo,queryWrapper);
        Page<OrdersDto> ordersDtoPage = new Page<OrdersDto>();
        BeanUtils.copyProperties(pageInfo,ordersDtoPage,"records");
        //完成订单详情的集合封装
        List<OrdersDto> ordersDtos = pageInfo.getRecords().stream().map(orders -> {
            OrdersDto ordersDto = new OrdersDto();
            //订单基本信息封装到ordersDto
            BeanUtils.copyProperties(orders, ordersDto);
            //订单详情集合
            LambdaQueryWrapper<OrderDetail> orderDetailLambdaQueryWrapper =
                    new LambdaQueryWrapper<>();
            orderDetailLambdaQueryWrapper.eq(OrderDetail::getOrderId, orders.getId());
            List<OrderDetail> ordersDetailList =
                    orderDetailService.list(orderDetailLambdaQueryWrapper);
            ordersDto.setOrderDetails(ordersDetailList);
            return ordersDto;
        }).collect(Collectors.toList());
        //手动封装ordersDtos分页中的records属性
        ordersDtoPage.setRecords(ordersDtos);
        return R.success(ordersDtoPage);
    }
    /**
     * 用户下单
     * @param orders
     * @return
     */
    @PostMapping("/submit")
    public R<Long> submit(@RequestBody Orders orders, HttpSession session){
        log.info("订单数据：{}",orders);

        try {
            // 生成订单ID（使用时间戳）
            Long orderId = System.currentTimeMillis();

            // 将订单信息存储到Session（无数据库模式）
            Map<String, Object> orderInfo = new HashMap<>();
            orderInfo.put("orderId", orderId);
            orderInfo.put("orders", orders);
            orderInfo.put("createTime", LocalDateTime.now());
            orderInfo.put("status", 1); // 1-待付款

            session.setAttribute("currentOrder", orderInfo);

            log.info("订单创建成功（无数据库模式），订单ID：{}", orderId);
            return R.success(orderId);
        } catch (Exception e) {
            log.error("创建订单失败：", e);
            return R.error("创建订单失败");
        }
    }
    @GetMapping("/userPage")
    public R<Page<OrdersDto>> ordersPage(Integer page, Integer pageSize, HttpSession session){
        try {
            // 创建模拟分页数据
            Page<OrdersDto> pageInfo = new Page<>(page, pageSize);
            List<OrdersDto> ordersList = new ArrayList<>();

            // 从Session获取当前订单
            Map<String, Object> currentOrder = (Map<String, Object>) session.getAttribute("currentOrder");
            if (currentOrder != null) {
                OrdersDto orderDto = new OrdersDto();
                orderDto.setId((Long) currentOrder.get("orderId"));
                orderDto.setNumber("RG" + currentOrder.get("orderId"));
                orderDto.setStatus((Integer) currentOrder.get("status"));
                orderDto.setOrderTime((LocalDateTime) currentOrder.get("createTime"));
                orderDto.setAmount(new BigDecimal("99.00")); // 模拟金额
                orderDto.setUserName("测试用户");
                orderDto.setPhone("<EMAIL>");
                orderDto.setAddress("测试地址");

                // 添加模拟订单详情
                List<OrderDetail> orderDetails = new ArrayList<>();
                OrderDetail detail = new OrderDetail();
                detail.setId(1L);
                detail.setName("测试菜品");
                detail.setNumber(1);
                detail.setAmount(new BigDecimal("99.00"));
                orderDetails.add(detail);
                orderDto.setOrderDetails(orderDetails);

                ordersList.add(orderDto);
            }

            pageInfo.setRecords(ordersList);
            pageInfo.setTotal(ordersList.size());

            return R.success(pageInfo);
        } catch (Exception e) {
            log.error("查询订单失败：", e);
            return R.error("查询订单失败");
        }
    }
}