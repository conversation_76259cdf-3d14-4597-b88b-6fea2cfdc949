#pay_success .divHead {
  width: 100%;
  height: 88rem;
  opacity: 1;
  background: #333333;
  position: relative;
}

#pay_success .divHead .divTitle {
  font-size: 18rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 25rem;
  letter-spacing: 0;
  position: absolute;
  bottom: 13rem;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#pay_success .divHead .divTitle i {
  margin-left: 16rem;
}

#pay_success .divHead .divTitle img {
  width: 18rem;
  height: 18rem;
  margin-right: 19rem;
}

#pay_success .divContent {
  height: calc(100vh - 88rem);
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
}

#pay_success .divContent img {
  margin-top: 148rem;
  margin-bottom: 19rem;
  width: 90rem;
  height: 86rem;
}

#pay_success .divContent .divSuccess {
  height: 33rem;
  opacity: 1;
  font-size: 24rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 33rem;
  margin-top: 19rem;
  margin-bottom: 10rem;
}

#pay_success .divContent .divDesc,
.divDesc1 {
  height: 22rem;
  opacity: 1;
  font-size: 16rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: #666666;
  line-height: 22rem;
}

#pay_success .divContent .divDesc1 {
  margin-top: 7rem;
  margin-bottom: 20rem;
}

#pay_success .divContent .btnView {
  width: 124rem;
  height: 36rem;
  opacity: 1;
  background: #ffc200;
  border-radius: 18px;
  opacity: 1;
  font-size: 15rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 21rem;
  letter-spacing: 0;
  line-height: 36rem;
}
