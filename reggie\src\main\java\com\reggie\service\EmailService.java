package com.reggie.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

/**
 * 邮件服务类
 */
@Service
@Slf4j
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 发送验证码邮件
     * @param to 收件人邮箱
     * @param code 验证码
     * @return 是否发送成功
     */
    public boolean sendVerificationCode(String to, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject("瑞吉外卖 - 登录验证码");
            message.setText("您的登录验证码是：" + code + "\n\n验证码有效期为5分钟，请及时使用。\n\n如果这不是您的操作，请忽略此邮件。\n\n瑞吉外卖团队");
            
            mailSender.send(message);
            log.info("验证码邮件发送成功，收件人：{}，验证码：{}", to, code);
            return true;
        } catch (Exception e) {
            log.error("验证码邮件发送失败，收件人：{}，错误信息：{}", to, e.getMessage());
            return false;
        }
    }
}
