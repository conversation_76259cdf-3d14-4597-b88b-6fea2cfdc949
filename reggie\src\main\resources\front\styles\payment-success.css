#payment-success {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #ffc200 0%, #ffed4e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-container {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 16px;
  padding: 40px 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 成功图标 */
.success-icon {
  margin-bottom: 24px;
}

.success-icon i {
  font-size: 64px;
  color: #67c23a;
  animation: successPulse 1.5s ease-in-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 成功信息 */
.success-info {
  margin-bottom: 32px;
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.success-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 订单信息 */
.order-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.info-item .value.amount {
  color: #ff6b35;
  font-size: 16px;
  font-weight: 600;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}

.action-buttons .el-button {
  flex: 1;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}

.action-buttons .el-button--default {
  background: #f5f5f5;
  border-color: #ddd;
  color: #666;
}

.action-buttons .el-button--default:hover {
  background: #e9e9e9;
  border-color: #ccc;
}

.action-buttons .el-button--primary {
  background: #ffc200;
  border-color: #ffc200;
}

.action-buttons .el-button--primary:hover {
  background: #e6ae00;
  border-color: #e6ae00;
}

/* 温馨提示 */
.tips {
  text-align: left;
}

.tips-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.tips-content {
  background: #fff9e6;
  border: 1px solid #ffd666;
  border-radius: 8px;
  padding: 16px;
  font-size: 12px;
  color: #666;
  line-height: 1.6;
}

.tips-content p {
  margin: 0 0 8px 0;
}

.tips-content p:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .success-container {
    padding: 32px 20px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
