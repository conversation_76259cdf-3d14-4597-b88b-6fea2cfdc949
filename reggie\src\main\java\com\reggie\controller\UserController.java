package com.reggie.controller;

import com.reggie.common.R;
import com.reggie.entity.User;
import com.reggie.service.EmailService;
import com.reggie.utils.ValidateCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    private EmailService emailService;

    // 邮箱格式验证正则表达式
    private static final String EMAIL_PATTERN =
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    /**
     * 发送邮箱验证码
     * @param user
     * @return
     */
    @PostMapping("/sendMsg")
    public R<String> sendMsg(@RequestBody User user, HttpSession session){
        try {
            //获取邮箱地址，兼容phone和email字段
            String email = user.getEmail();
            if (StringUtils.isEmpty(email)) {
                email = user.getPhone(); // 兼容前端可能还在使用phone字段
            }

            log.info("发送验证码请求 - 邮箱：{}", email);

            if(StringUtils.isNotEmpty(email)){
                // 验证邮箱格式
                if (!isValidEmail(email)) {
                    return R.error("邮箱格式不正确");
                }

                // 限制只能使用测试邮箱
                if (!"<EMAIL>".equals(email)) {
                    return R.error("当前仅支持测试邮箱：<EMAIL>");
                }

                //生成随机的6位验证码
                String code = ValidateCodeUtils.generateValidateCode(6).toString();
                log.info("生成验证码：{}，发送至邮箱：{}", code, email);

                // 发送邮件
                boolean sendSuccess = emailService.sendVerificationCode(email, code);
                if (sendSuccess) {
                    // 将验证码存储到session中，有效期5分钟（用于日志记录）
                    session.setAttribute(email, code);
                    session.setMaxInactiveInterval(300); // 5分钟
                    log.info("验证码已发送并存储到Session，邮箱：{}，验证码：{}", email, code);
                    return R.success("邮箱验证码发送成功，请查收邮件");
                } else {
                    log.error("邮件发送失败，邮箱：{}", email);
                    return R.error("邮箱验证码发送失败，请稍后重试");
                }
            }

            return R.error("邮箱地址不能为空");
        } catch (Exception e) {
            log.error("发送验证码过程中发生异常：", e);
            return R.error("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 验证邮箱格式
     * @param email
     * @return
     */
    private boolean isValidEmail(String email) {
        return pattern.matcher(email).matches();
    }

    /**
     * 移动端用户登录
     * @param map
     * @param session
     * @return
     */
    @PostMapping("/login")
    public R<User> login(@RequestBody Map map, HttpSession session){
        log.info("登录请求参数：{}", map.toString());

        try {
            // 获取邮箱地址，兼容phone和email两种参数名
            String email = null;
            if (map.get("email") != null) {
                email = map.get("email").toString();
            } else if (map.get("phone") != null) {
                email = map.get("phone").toString();
            }

            // 获取验证码
            Object codeObj = map.get("code");
            if (email == null || codeObj == null) {
                return R.error("邮箱地址和验证码不能为空");
            }

            String code = codeObj.toString();
            log.info("登录验证 - 邮箱：{}，验证码：{}", email, code);

            // 验证邮箱格式
            if (!isValidEmail(email)) {
                return R.error("邮箱格式不正确");
            }

            //从Session中获取保存的验证码（仅用于日志记录）
            Object codeInSession = session.getAttribute(email);
            log.info("用户输入验证码：{}，Session中的验证码：{}", code, codeInSession);

            // 简化验证逻辑：任何验证码都能通过验证
            log.info("验证码验证通过（简化模式），邮箱：{}", email);

            // 创建虚拟用户对象，不涉及数据库操作
            User user = new User();
            user.setId(System.currentTimeMillis()); // 使用时间戳作为临时ID
            user.setEmail(email);
            user.setStatus(1);

            // 将用户信息存储到Session
            session.setAttribute("user", user.getId());
            session.setAttribute("userEmail", email);

            // 清除验证码
            session.removeAttribute(email);
            log.info("用户登录成功（无数据库模式），邮箱：{}，临时ID：{}", email, user.getId());

            return R.success(user);
        } catch (Exception e) {
            log.error("登录过程中发生异常：", e);
            return R.error("登录失败：" + e.getMessage());
        }
    }
    /**
     * 用户退出
     */
    @PostMapping("/loginout")
    public R<String> logout(HttpServletRequest request){
        //清理Session中保存的当前登录员工的id
        request.getSession().removeAttribute("user");
        return R.success("退出成功");
    }
}
