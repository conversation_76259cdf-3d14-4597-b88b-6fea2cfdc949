package com.reggie.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.reggie.common.R;
import com.reggie.entity.User;
import com.reggie.service.EmailService;
import com.reggie.service.UserService;
import com.reggie.utils.ValidateCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private EmailService emailService;

    // 邮箱格式验证正则表达式
    private static final String EMAIL_PATTERN =
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    /**
     * 发送邮箱验证码
     * @param user
     * @return
     */
    @PostMapping("/sendMsg")
    public R<String> sendMsg(@RequestBody User user, HttpSession session){
        try {
            //获取邮箱地址，兼容phone和email字段
            String email = user.getEmail();
            if (StringUtils.isEmpty(email)) {
                email = user.getPhone(); // 兼容前端可能还在使用phone字段
            }

            log.info("发送验证码请求 - 邮箱：{}", email);

            if(StringUtils.isNotEmpty(email)){
                // 验证邮箱格式
                if (!isValidEmail(email)) {
                    return R.error("邮箱格式不正确");
                }

                // 限制只能使用测试邮箱
                if (!"<EMAIL>".equals(email)) {
                    return R.error("当前仅支持测试邮箱：<EMAIL>");
                }

                // 临时使用固定验证码进行测试（生产环境请删除此段代码）
                String code = "123456";
                log.info("使用固定验证码进行测试：{}，邮箱：{}", code, email);

                // 将验证码存储到session中，有效期5分钟
                session.setAttribute(email, code);
                session.setMaxInactiveInterval(300); // 5分钟
                log.info("验证码已存储到Session，邮箱：{}，验证码：{}", email, code);

                // 尝试发送邮件（如果失败也不影响登录测试）
                try {
                    emailService.sendVerificationCode(email, code);
                    log.info("邮件发送成功");
                } catch (Exception e) {
                    log.warn("邮件发送失败，但使用固定验证码：{}", e.getMessage());
                }

                return R.success("验证码发送成功，测试验证码：123456");
            }

            return R.error("邮箱地址不能为空");
        } catch (Exception e) {
            log.error("发送验证码过程中发生异常：", e);
            return R.error("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 验证邮箱格式
     * @param email
     * @return
     */
    private boolean isValidEmail(String email) {
        return pattern.matcher(email).matches();
    }

    /**
     * 移动端用户登录
     * @param map
     * @param session
     * @return
     */
    @PostMapping("/login")
    public R<User> login(@RequestBody Map map, HttpSession session){
        log.info("登录请求参数：{}", map.toString());

        try {
            // 获取邮箱地址，兼容phone和email两种参数名
            String email = null;
            if (map.get("email") != null) {
                email = map.get("email").toString();
            } else if (map.get("phone") != null) {
                email = map.get("phone").toString();
            }

            // 获取验证码
            Object codeObj = map.get("code");
            if (email == null || codeObj == null) {
                return R.error("邮箱地址和验证码不能为空");
            }

            String code = codeObj.toString();
            log.info("登录验证 - 邮箱：{}，验证码：{}", email, code);

            // 验证邮箱格式
            if (!isValidEmail(email)) {
                return R.error("邮箱格式不正确");
            }

            //从Session中获取保存的验证码
            Object codeInSession = session.getAttribute(email);
            log.info("Session中的验证码：{}", codeInSession);

            //进行验证码的比对（页面提交的验证码和Session中保存的验证码比对）
            if(codeInSession != null && codeInSession.equals(code)){
                //如果能够比对成功，说明登录成功

                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(User::getEmail, email);

                User user = userService.getOne(queryWrapper);
                if(user == null){
                    //判断当前邮箱对应的用户是否为新用户，如果是新用户就自动完成注册
                    user = new User();
                    user.setEmail(email);
                    user.setStatus(1);
                    userService.save(user);
                    log.info("新用户注册成功：{}", email);
                }
                session.setAttribute("user",user.getId());
                // 清除验证码
                session.removeAttribute(email);
                log.info("用户登录成功：{}", email);
                return R.success(user);
            }
            return R.error("验证码错误或已过期");
        } catch (Exception e) {
            log.error("登录过程中发生异常：", e);
            return R.error("登录失败：" + e.getMessage());
        }
    }
    /**
     * 用户退出
     */
    @PostMapping("/loginout")
    public R<String> logout(HttpServletRequest request){
        //清理Session中保存的当前登录员工的id
        request.getSession().removeAttribute("user");
        return R.success("退出成功");
    }
}
