# 简化邮箱验证系统说明

## 功能概述
实现了简化的邮箱验证登录系统：发送真实验证码到邮箱，但验证时任何验证码都能通过，无需数据库操作。

## 核心特性

### 🔑 关键设计
- **真实邮件发送**：生成随机6位验证码并发送到指定邮箱
- **简化验证**：任何验证码输入都能通过验证
- **无数据库依赖**：不查询、不更新任何数据库表
- **Session管理**：使用Session维护用户登录状态

## 登录流程

1. 用户在登录页面输入邮箱地址（<EMAIL>）
2. 点击"获取验证码"按钮
3. 系统生成6位随机验证码并发送到该邮箱
4. 用户输入**任意验证码**（不需要是邮箱中收到的验证码）
5. 系统自动通过验证（简化模式）
6. 验证成功后重定向到订餐页面

## 核心特性

1. **真实邮件发送**：确实会发送验证码到邮箱
2. **简化验证**：任何验证码输入都能通过验证
3. **邮箱格式验证**：使用正则表达式验证邮箱格式
4. **测试限制**：当前仅支持测试邮箱 <EMAIL>
5. **无数据库操作**：不查询或更新任何数据库表
6. **Session管理**：使用Session维护登录状态
7. **异常处理**：邮件发送失败时有相应的错误处理

## 技术实现

### 后端修改
1. **UserController.sendMsg()**：
   - 生成真实的6位随机验证码
   - 调用EmailService发送邮件
   - 验证邮箱格式和限制测试邮箱

2. **UserController.login()**：
   - 简化验证逻辑：任何验证码都能通过
   - 创建虚拟用户对象，不涉及数据库
   - 使用时间戳作为临时用户ID
   - 将用户信息存储到Session

3. **EmailService**：
   - 配置QQ邮箱SMTP
   - 发送包含验证码的邮件

### 前端保持不变
- 邮箱输入框
- 验证码输入框
- 邮箱格式验证
- 登录逻辑

## 测试说明

### 前置条件
1. 确保Spring Boot应用正常启动
2. 确保邮件服务配置正确
3. 确保QQ邮箱授权码有效
4. **无需任何数据库操作**

### 测试步骤
1. 启动Spring Boot应用
2. 访问 `http://localhost:8080/front/page/login.html`
3. 输入邮箱地址：<EMAIL>
4. 点击"获取验证码"
5. 检查邮箱是否收到验证码（可选）
6. 输入**任意验证码**（如：123456、000000等）
7. 点击登录，系统会自动通过验证
8. 验证是否成功跳转到订餐页面

## 验证示例

### 有效的测试场景：
- 邮箱：<EMAIL>，验证码：123456 ✅
- 邮箱：<EMAIL>，验证码：000000 ✅
- 邮箱：<EMAIL>，验证码：abcdef ✅
- 邮箱：<EMAIL>，验证码：任意内容 ✅

### 无效的测试场景：
- 邮箱：其他邮箱地址 ❌（只支持测试邮箱）
- 邮箱格式错误 ❌（邮箱格式验证）

## 注意事项

1. **邮箱限制**：当前仅支持测试邮箱 <EMAIL>
2. **验证码验证**：任何验证码都能通过，无需输入真实验证码
3. **邮件发送**：会真实发送验证码到邮箱，但验证时不检查正确性
4. **无数据库依赖**：系统不会查询或修改任何数据库表
5. **Session管理**：登录状态通过Session维护
6. **网络要求**：确保能够访问QQ邮箱SMTP服务器

## 日志信息

系统会输出以下关键日志：
- "生成验证码：xxx，发送至邮箱：xxx"
- "验证码已发送并存储到Session"
- "用户输入验证码：xxx，Session中的验证码：xxx"
- "验证码验证通过（简化模式）"
- "用户登录成功（无数据库模式）"
