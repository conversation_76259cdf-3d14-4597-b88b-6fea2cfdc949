#payment {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
}

.divHead {
  width: 100%;
  height: 44px;
  background: #ffc200;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.divTitle {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  position: relative;
}

.divTitle i {
  position: absolute;
  left: 16px;
  font-size: 20px;
  cursor: pointer;
}

/* 订单摘要 */
.order-summary {
  margin-top: 54px;
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

.summary-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.dish-list {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.dish-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.dish-name {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.dish-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity {
  font-size: 14px;
  color: #666;
}

.price {
  font-size: 14px;
  color: #ff6b35;
  font-weight: 500;
}

.address-info {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.address-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.address-detail {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.total-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.total-amount .amount {
  color: #ff6b35;
  font-size: 18px;
}

/* 支付方式选择 */
.payment-methods {
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

.method-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.method-option.active {
  border-color: #ffc200;
  background: #fffbf0;
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  margin-right: 12px;
}

.method-option.active .method-icon {
  background: #ffc200;
  color: #fff;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 12px;
  color: #666;
}

.method-radio {
  font-size: 20px;
  color: #ccc;
}

.method-option.active .method-radio {
  color: #ffc200;
}

/* 二维码支付 */
.qrcode-payment {
  background: #fff;
  padding: 20px;
  text-align: center;
}

.qrcode-container {
  max-width: 300px;
  margin: 0 auto;
}

.qrcode-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  margin: 0 auto 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.qrcode-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #666;
}

.qrcode-amount {
  font-size: 18px;
  font-weight: 500;
  color: #ff6b35;
  margin-bottom: 16px;
}

.qrcode-tips {
  text-align: left;
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 密码支付 */
.password-payment {
  background: #fff;
  padding: 20px;
}

.password-container {
  max-width: 300px;
  margin: 0 auto;
}

.password-title {
  font-size: 16px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.password-input {
  margin-bottom: 16px;
}

.password-input .el-input__inner {
  height: 48px;
  font-size: 16px;
  text-align: center;
  letter-spacing: 4px;
}

.password-tips {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 24px;
}

.password-actions {
  text-align: center;
}

.password-actions .el-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  background: #ffc200;
  border-color: #ffc200;
}

.password-actions .el-button:hover {
  background: #e6ae00;
  border-color: #e6ae00;
}
