// 生成支付二维码
function generateQRCodeApi(orderId) {
    return $axios({
        'url': `/payment/qrcode/${orderId}`,
        'method': 'get'
    })
}

// 密码支付
function passwordPaymentApi(data) {
    return $axios({
        'url': '/payment/password',
        'method': 'post',
        data
    })
}

// 查询支付状态
function getPaymentStatusApi(orderId) {
    return $axios({
        'url': `/payment/status/${orderId}`,
        'method': 'get'
    })
}
