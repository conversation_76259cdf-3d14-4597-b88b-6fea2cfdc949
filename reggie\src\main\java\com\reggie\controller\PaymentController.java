package com.reggie.controller;

import com.reggie.common.R;
import com.reggie.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * 支付控制器
 */
@RestController
@RequestMapping("/payment")
@Slf4j
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    /**
     * 生成支付二维码
     * @param orderId 订单ID
     * @return 二维码图片URL
     */
    @GetMapping("/qrcode/{orderId}")
    public R<String> generateQRCode(@PathVariable Long orderId) {
        log.info("生成支付二维码，订单ID：{}", orderId);
        try {
            String qrCodeUrl = paymentService.generateQRCode(orderId);
            return R.success(qrCodeUrl);
        } catch (Exception e) {
            log.error("生成二维码失败：", e);
            return R.error("生成二维码失败");
        }
    }

    /**
     * 密码支付
     * @param paymentData 支付数据
     * @param session Session
     * @return 支付结果
     */
    @PostMapping("/password")
    public R<String> passwordPayment(@RequestBody Map<String, Object> paymentData, HttpSession session) {
        log.info("密码支付请求：{}", paymentData);
        
        try {
            Long orderId = Long.valueOf(paymentData.get("orderId").toString());
            String password = paymentData.get("password").toString();
            
            // 验证支付密码
            if (!"123456".equals(password)) {
                return R.error("支付密码错误");
            }
            
            // 处理支付
            boolean paymentResult = paymentService.processPasswordPayment(orderId, session);
            
            if (paymentResult) {
                log.info("订单支付成功，订单ID：{}", orderId);
                return R.success("支付成功");
            } else {
                return R.error("支付失败，请重试");
            }
        } catch (Exception e) {
            log.error("密码支付失败：", e);
            return R.error("支付失败：" + e.getMessage());
        }
    }

    /**
     * 查询支付状态
     * @param orderId 订单ID
     * @return 支付状态
     */
    @GetMapping("/status/{orderId}")
    public R<Map<String, Object>> getPaymentStatus(@PathVariable Long orderId) {
        log.info("查询支付状态，订单ID：{}", orderId);
        try {
            Map<String, Object> status = paymentService.getPaymentStatus(orderId);
            return R.success(status);
        } catch (Exception e) {
            log.error("查询支付状态失败：", e);
            return R.error("查询支付状态失败");
        }
    }
}
