#no_wifi .divHead {
  width: 100%;
  height: 88rem;
  opacity: 1;
  background: #333333;
  position: relative;
}

#no_wifi .divHead .divTitle {
  font-size: 18rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 25rem;
  letter-spacing: 0;
  position: absolute;
  bottom: 13rem;
  width: 100%;
}

#no_wifi .divHead .divTitle i {
  position: absolute;
  left: 16rem;
  top: 50%;
  transform: translate(0, -50%);
}

#no_wifi .divContent {
  height: calc(100vh - 88rem);
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
}

#no_wifi .divContent img {
  width: 239rem;
  height: 130rem;
  margin-top: 104rem;
  margin-bottom: 19rem;
}

#no_wifi .divContent .divDesc {
  height: 33rem;
  opacity: 1;
  font-size: 24rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 33rem;
  letter-spacing: 0;
  margin-bottom: 20rem;
}

#no_wifi .divContent .btnRefresh {
  width: 124rem;
  height: 36rem;
  opacity: 1;
  background: #ffc200;
  border-radius: 18px;
  opacity: 1;
  font-size: 15rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 21rem;
  letter-spacing: 0;
  line-height: 36rem;
}
