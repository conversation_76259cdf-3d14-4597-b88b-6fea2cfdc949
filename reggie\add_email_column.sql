-- 检查并为user表添加email字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'user'
     AND table_schema = 'db_reggie'
     AND column_name = 'email') > 0,
    'SELECT "email字段已存在" as message',
    'ALTER TABLE `user` ADD COLUMN `email` varchar(100) DEFAULT NULL COMMENT "邮箱地址" AFTER `phone`'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为测试邮箱创建用户记录（如果不存在）
INSERT INTO `user` (`id`, `email`, `status`)
SELECT 1548719795, '<EMAIL>', 1
WHERE NOT EXISTS (SELECT 1 FROM `user` WHERE `email` = '<EMAIL>');
